using System.Text.Json.Serialization;

namespace SocketMonitor.Models;

/// <summary>
/// Discord webhook message payload
/// </summary>
public class DiscordWebhookMessage
{
    [JsonPropertyName("content")]
    public string? Content { get; set; }

    [JsonPropertyName("username")]
    public string? Username { get; set; }

    [JsonPropertyName("avatar_url")]
    public string? AvatarUrl { get; set; }

    [JsonPropertyName("embeds")]
    public List<DiscordEmbed>? Embeds { get; set; }
}

/// <summary>
/// Discord embed object
/// </summary>
public class DiscordEmbed
{
    [JsonPropertyName("title")]
    public string? Title { get; set; }

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("timestamp")]
    public DateTime? Timestamp { get; set; }

    [JsonPropertyName("color")]
    public int? Color { get; set; }

    [JsonPropertyName("footer")]
    public DiscordEmbedFooter? Footer { get; set; }

    [JsonPropertyName("image")]
    public DiscordEmbedImage? Image { get; set; }

    [JsonPropertyName("thumbnail")]
    public DiscordEmbedThumbnail? Thumbnail { get; set; }

    [JsonPropertyName("author")]
    public DiscordEmbedAuthor? Author { get; set; }

    [JsonPropertyName("fields")]
    public List<DiscordEmbedField>? Fields { get; set; }
}

/// <summary>
/// Discord embed footer
/// </summary>
public class DiscordEmbedFooter
{
    [JsonPropertyName("text")]
    public string Text { get; set; } = string.Empty;

    [JsonPropertyName("icon_url")]
    public string? IconUrl { get; set; }
}

/// <summary>
/// Discord embed image
/// </summary>
public class DiscordEmbedImage
{
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Discord embed thumbnail
/// </summary>
public class DiscordEmbedThumbnail
{
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Discord embed author
/// </summary>
public class DiscordEmbedAuthor
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("icon_url")]
    public string? IconUrl { get; set; }
}

/// <summary>
/// Discord embed field
/// </summary>
public class DiscordEmbedField
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("value")]
    public string Value { get; set; } = string.Empty;

    [JsonPropertyName("inline")]
    public bool? Inline { get; set; }
}

/// <summary>
/// Discord notification configuration
/// </summary>
public class DiscordNotificationConfig
{
    public string WebhookUrl { get; set; } = string.Empty;
    public string? DefaultUsername { get; set; }
    public string? DefaultAvatarUrl { get; set; }
    public int TimeoutSeconds { get; set; } = 30;
}
