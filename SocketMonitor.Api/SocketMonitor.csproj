<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.21" />
    <PackageReference Include="Hangfire.Storage.SQLite" Version="0.4.2" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.8" />
    <PackageReference Include="Microsoft.Playwright" Version="1.55.0-beta-4" />
  </ItemGroup>

</Project>
