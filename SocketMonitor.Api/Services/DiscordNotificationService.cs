using System.Text;
using System.Text.Json;

namespace SocketMonitor.Services;

public class DiscordService
{
    private readonly string _webhookUrl = "https://discord.com/api/webhooks/1412024190684495965/wVdPhz2N3dDNsgrQXpUAGYLGVjsLyYHlTJZEUZtDTpzGztMqRFf_9ifLETv3hQYrt_5W";
    private static readonly HttpClient HttpClient = new HttpClient();
    
    public async Task SendMessageAsync(string message)
    {
        var payload = new
        {
            content = message
        };

        var json = JsonSerializer.Serialize(payload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync(_webhookUrl, content);
        response.EnsureSuccessStatusCode();
    }
}