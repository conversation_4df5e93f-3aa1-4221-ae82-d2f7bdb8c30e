using Microsoft.Playwright;
using System.Text.Json;

namespace SocketMonitor.Services;

public class WebService
{
    private const string SessionFilePath = "session.json";
    public async Task<string[]> OpenAndWaitForMessages(string url, int waitingTime)
    {
        using var playwright = await Playwright.CreateAsync();
        BrowserTypeLaunchOptions options = new BrowserTypeLaunchOptions()
        {
            ExecutablePath = "/home/<USER>/.cache/ms-playwright/chromium-1187/chrome-linux/chrome",
            Headless = false
        };
        await using var browser = await playwright.Chromium.LaunchAsync(options);

        // Try to create context with existing storage state if available
        BrowserNewContextOptions contextOptions = new BrowserNewContextOptions();
        if (File.Exists(SessionFilePath))
        {
            contextOptions.StorageStatePath = SessionFilePath;
        }

        var context = await browser.NewContextAsync(contextOptions);
        var page = await context.NewPageAsync();
        
        var messages = new List<string>();

        page.WebSocket += (_, ws) =>
        {
            ws.FrameReceived += (_, msg) =>
            {
                messages.Add(msg.Text);
            };
        };

        await page.GotoAsync(url);
        
        await TryRestoreSessionOrLoginAndSaveNew(page);

        await Task.Delay(TimeSpan.FromSeconds(waitingTime));

        Console.WriteLine("Messages:");
        foreach (var m in messages)
        {
            Console.WriteLine(m);
        }

        string[] result = messages.ToArray();

        return result;

    }

    private async Task TryRestoreSessionOrLoginAndSaveNew(IPage page)
    {
        try
        {
            // Try to restore session from saved state
            if (await TryRestoreSession(page))
            {
                Console.WriteLine("Session restored successfully");
                return;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to restore session: {ex.Message}");
        }

        // If session restoration failed, attempt login
        try
        {
            if (await TryLogin(page))
            {
                // Save the new session state after successful login
                await SaveSessionState(page);
                Console.WriteLine("Login successful and session saved");
            }
            else
            {
                Console.WriteLine("Login failed");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Login process failed: {ex.Message}");
        }
    }

    private async Task<bool> TryRestoreSession(IPage page)
    {
        if (!File.Exists(SessionFilePath))
        {
            Console.WriteLine("No session file found");
            return false;
        }

        try
        {
            // Since we already loaded the storage state when creating the context,
            // we just need to check if we're logged in
            Console.WriteLine("Storage state loaded from file, checking login status...");

            // Check if we're still logged in by looking for login indicators
            return await IsLoggedIn(page);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error restoring session: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> TryLogin(IPage page)
    {
        try
        {
            // First, look for "Log in" button to navigate to login page
            Console.WriteLine("Looking for 'Log in' button to navigate to login page...");

            try
            {
                var loginPageButton = await page.WaitForSelectorAsync("button:has-text('Log In')",
                    new PageWaitForSelectorOptions { Timeout = 5000 });

                if (loginPageButton != null)
                {
                    Console.WriteLine("Found 'Log in' button, clicking to navigate to login page...");
                    await loginPageButton.ClickAsync();

                    // Wait for navigation to login page
                    await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
                }
            }
            catch (TimeoutException)
            {
                Console.WriteLine("'Log in' button not found, assuming we're already on login page or logged in");
            }

            // Now look for login form elements
            var usernameSelector = "input[type='email'], input[type='text'], input[name*='user'], input[name*='email'], input[id*='user'], input[id*='email']";
            var passwordSelector = "input[type='password'], input[name*='pass'], input[id*='pass']";
            var submitButtonSelector = "button[type='submit'], input[type='submit'], button:has-text('Login'), button:has-text('Sign in'), button:has-text('Submit')";

            // Wait for login form to be available
            try
            {
                await page.WaitForSelectorAsync(usernameSelector, new PageWaitForSelectorOptions { Timeout = 5000 });
            }
            catch (TimeoutException)
            {
                Console.WriteLine("Login form not found - user might already be logged in");
                return await IsLoggedIn(page);
            }

            var usernameField = await page.QuerySelectorAsync(usernameSelector);
            var passwordField = await page.QuerySelectorAsync(passwordSelector);
            var submitButton = await page.QuerySelectorAsync(submitButtonSelector);

            if (usernameField == null || passwordField == null || submitButton == null)
            {
                Console.WriteLine("Login form elements not found");
                return false;
            }

            // TODO: Replace with actual credentials from configuration
            // await usernameField.FillAsync("your-username");
            // await passwordField.FillAsync("your-password");
            // await submitButton.ClickAsync();

            // Wait for navigation or success indicator
            // await page.WaitForNavigationAsync();

            // For now, return false since we don't have actual credentials
            Console.WriteLine("Login form found but no credentials configured");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Login attempt failed: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> IsLoggedIn(IPage page)
    {
        try
        {
            // Look for indicators that user is logged in
            // This could be checking for user profile elements, dashboard content, etc.
            var loggedInIndicators = new[]
            {
                "[data-testid*='user']",
                ".user-profile",
                ".dashboard",
                "[class*='logged-in']",
                "button:has-text('Logout')",
                "button:has-text('Sign out')"
            };

            foreach (var selector in loggedInIndicators)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        Console.WriteLine($"Found logged-in indicator: {selector}");
                        return true;
                    }
                }
                catch
                {
                    // Continue checking other selectors
                }
            }

            // Check if we're NOT on a login page
            var loginIndicators = new[]
            {
                "input[type='password']",
                "button:has-text('Login')",
                "button:has-text('Sign in')",
                ".login-form",
                "[class*='login']"
            };

            foreach (var selector in loginIndicators)
            {
                try
                {
                    var element = await page.QuerySelectorAsync(selector);
                    if (element != null)
                    {
                        Console.WriteLine($"Found login indicator, not logged in: {selector}");
                        return false;
                    }
                }
                catch
                {
                    // Continue checking
                }
            }

            // If no clear indicators, assume we need to login
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error checking login status: {ex.Message}");
            return false;
        }
    }

    private async Task SaveSessionState(IPage page)
    {
        try
        {
            // Use Playwright's built-in storage state functionality
            await page.Context.StorageStateAsync(new BrowserContextStorageStateOptions
            {
                Path = SessionFilePath
            });

            Console.WriteLine($"Session saved to {SessionFilePath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to save session: {ex.Message}");
        }
    }
}
