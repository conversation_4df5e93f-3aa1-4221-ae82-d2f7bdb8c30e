# Copyright (c) Microsoft Corporation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

StackFrame:
  type: object
  properties:
    file: string
    line: number
    column: number
    function: string?

# This object can be send with any rpc call in the "metadata" field.

Metadata:
  type: object
  properties:
    location:
      type: object?
      properties:
        file: string
        line: number?
        column: number?
    title: string?
    internal: boolean?
    # Test runner step id.
    stepId: string?

ClientSideCallMetadata:
  type: object
  properties:
    id: number
    stack:
      type: array?
      items: StackFrame

Point:
  type: object
  properties:
    x: number
    y: number


Rect:
  type: object
  properties:
    x: number
    y: number
    width: number
    height: number


SerializedValue:
  type: object
  # Exactly one of the properties must be present.
  properties:
    n: number?
    b: boolean?
    s: string?
    v:
      type: enum?
      literals:
      - null
      - undefined
      - NaN
      - Infinity
      - -Infinity
      - "-0"
    # String representation of the Date.
    d: string?
    # String representation of the URL.
    u: string?
    # String representation of BigInt.
    bi: string?
    # Typed array.
    ta:
      type: object?
      properties:
        b: binary
        k:
          type: enum
          literals:
          - i8
          - ui8
          - ui8c
          - i16
          - ui16
          - i32
          - ui32
          - f32
          - f64
          - bi64
          - bui64
    # Serialized Error object.
    e:
      type: object?
      properties:
        m: string
        n: string
        s: string
    # Regular expression pattern and flags.
    r:
      type: object?
      properties:
        p: string
        f: string
    a:
      type: array?
      items: SerializedValue
    # Object with keys and values.
    o:
      type: array?
      items:
        type: object
        properties:
          k: string
          v: SerializedValue
    # An index in the handles array from SerializedArgument.
    h: number?
    # Index of the object in value-type for circular reference resolution.
    id: number?
    # Ref to the object in value-type for circular reference resolution.
    ref: number?


# Represents a value with handle references.
SerializedArgument:
  type: object
  properties:
    value: SerializedValue
    handles:
      type: array
      items: Channel


ExpectedTextValue:
  type: object
  properties:
    string: string?
    regexSource: string?
    regexFlags: string?
    matchSubstring: boolean?
    ignoreCase: boolean?
    normalizeWhiteSpace: boolean?


SelectorEngine:
  type: object
  properties:
    name: string
    source: string
    contentScript: boolean?


AXNode:
  type: object
  properties:
    role: string
    name: string
    valueString: string?
    valueNumber: number?
    description: string?
    keyshortcuts: string?
    roledescription: string?
    valuetext: string?
    disabled: boolean?
    expanded: boolean?
    focused: boolean?
    modal: boolean?
    multiline: boolean?
    multiselectable: boolean?
    readonly: boolean?
    required: boolean?
    selected: boolean?
    checked:
      type: enum?
      literals:
      - checked
      - unchecked
      - mixed
    pressed:
      type: enum?
      literals:
      - pressed
      - released
      - mixed
    level: number?
    valuemin: number?
    valuemax: number?
    autocomplete: string?
    haspopup: string?
    invalid: string?
    orientation: string?
    children:
      type: array?
      items: AXNode


SetNetworkCookie:
  type: object
  properties:
    name: string
    value: string
    url: string?
    domain: string?
    path: string?
    expires: number?
    httpOnly: boolean?
    secure: boolean?
    sameSite:
      type: enum?
      literals:
      - Strict
      - Lax
      - None
    partitionKey: string?
    _crHasCrossSiteAncestor: boolean?


NetworkCookie:
  type: object
  properties:
    name: string
    value: string
    domain: string
    path: string
    expires: number
    httpOnly: boolean
    secure: boolean
    sameSite:
      type: enum
      literals:
      - Strict
      - Lax
      - None
    partitionKey: string?
    _crHasCrossSiteAncestor: boolean?


NameValue:
  type: object
  properties:
    name: string
    value: string

IndexedDBDatabase:
  type: object
  properties:
    name: string
    version: number
    stores:
      type: array
      items:
        type: object
        properties:
          name: string
          autoIncrement: boolean
          keyPath: string?
          keyPathArray:
            type: array?
            items: string
          records:
            type: array
            items:
              type: object
              properties:
                key: json?
                keyEncoded: json?
                value: json?
                valueEncoded: json?
          indexes:
            type: array
            items:
              type: object
              properties:
                name: string
                keyPath: string?
                keyPathArray:
                  type: array?
                  items: string
                multiEntry: boolean
                unique: boolean

SetOriginStorage:
  type: object
  properties:
    origin: string
    localStorage:
      type: array
      items: NameValue
    indexedDB:
      type: array?
      items: IndexedDBDatabase

OriginStorage:
  type: object
  properties:
    origin: string
    localStorage:
      type: array
      items: NameValue
    indexedDB:
      type: array?
      items: IndexedDBDatabase

SerializedError:
  type: object
  properties:
    error:
      type: object?
      properties:
        message: string
        name: string
        stack: string?
    value: SerializedValue?


RecordHarOptions:
  type: object
  properties:
    zip: boolean?
    content:
      type: enum?
      literals:
      - embed
      - attach
      - omit
    mode:
      type: enum?
      literals:
      - full
      - minimal
    urlGlob: string?
    urlRegexSource: string?
    urlRegexFlags: string?


FormField:
  type: object
  properties:
    name: string
    value: string?
    file:
      type: object?
      properties:
        name: string
        mimeType: string?
        buffer: binary

APIRequestContext:
  type: interface

  initializer:
    tracing: Tracing

  commands:

    fetch:
      title: '{method} "{url}"'
      parameters:
        url: string
        encodedParams: string?
        params:
          type: array?
          items: NameValue
        method: string?
        headers:
          type: array?
          items: NameValue
        postData: binary?
        jsonData: string?
        formData:
          type: array?
          items: NameValue
        multipartData:
          type: array?
          items: FormField
        timeout: number
        failOnStatusCode: boolean?
        ignoreHTTPSErrors: boolean?
        maxRedirects: number?
        maxRetries: number?
      returns:
        response: APIResponse

    fetchResponseBody:
      internal: true
      parameters:
        fetchUid: string
      returns:
        binary: binary?

    fetchLog:
      internal: true
      parameters:
        fetchUid: string
      returns:
        log:
          type: array
          items: string

    storageState:
      internal: true
      parameters:
        indexedDB: boolean?
      returns:
        cookies:
          type: array
          items: NetworkCookie
        origins:
          type: array
          items: OriginStorage

    disposeAPIResponse:
      internal: true
      parameters:
        fetchUid: string

    dispose:
      internal: true
      parameters:
        reason: string?


APIResponse:
  type: object
  properties:
    fetchUid: string
    url: string
    status: number
    statusText: string
    headers:
      type: array
      items: NameValue


LifecycleEvent:
  type: enum
  literals:
  - load
  - domcontentloaded
  - networkidle
  - commit

CommonScreenshotOptions:
  type: mixin
  properties:
    omitBackground: boolean?
    caret:
      type: enum?
      literals:
      - hide
      - initial
    animations:
      type: enum?
      literals:
      - disabled
      - allow
    scale:
      type: enum?
      literals:
      - css
      - device
    mask:
      type: array?
      items:
        type: object
        properties:
          frame: Frame
          selector: string
    maskColor: string?
    style: string?

LaunchOptions:
  type: mixin
  properties:
    channel: string?
    executablePath: string?
    args:
      type: array?
      items: string
    ignoreAllDefaultArgs: boolean?
    ignoreDefaultArgs:
      type: array?
      items: string
    assistantMode: boolean?
    handleSIGINT: boolean?
    handleSIGTERM: boolean?
    handleSIGHUP: boolean?
    timeout: number
    env:
      type: array?
      items: NameValue
    headless: boolean?
    devtools: boolean?
    proxy:
      type: object?
      properties:
        server: string
        bypass: string?
        username: string?
        password: string?
    downloadsPath: string?
    tracesDir: string?
    chromiumSandbox: boolean?
    firefoxUserPrefs: json?
    cdpPort: number?


ContextOptions:
  type: mixin
  properties:
    noDefaultViewport: boolean?
    viewport:
      type: object?
      properties:
        width: number
        height: number
    screen:
      type: object?
      properties:
        width: number
        height: number
    ignoreHTTPSErrors: boolean?
    clientCertificates:
      type: array?
      items:
        type: object
        properties:
          origin: string
          cert: binary?
          key: binary?
          passphrase: string?
          pfx: binary?
    javaScriptEnabled: boolean?
    bypassCSP: boolean?
    userAgent: string?
    locale: string?
    timezoneId: string?
    geolocation:
      type: object?
      properties:
        longitude: number
        latitude: number
        accuracy: number?
    permissions:
      type: array?
      items: string
    extraHTTPHeaders:
      type: array?
      items: NameValue
    offline: boolean?
    httpCredentials:
      type: object?
      properties:
        username: string
        password: string
        origin: string?
        send:
          type: enum?
          literals:
          - always
          - unauthorized
    deviceScaleFactor: number?
    isMobile: boolean?
    hasTouch: boolean?
    colorScheme:
      type: enum?
      literals:
      - dark
      - light
      - no-preference
      - no-override
    reducedMotion:
      type: enum?
      literals:
      - reduce
      - no-preference
      - no-override
    forcedColors:
      type: enum?
      literals:
      - active
      - none
      - no-override
    acceptDownloads:
      type: enum?
      literals:
      - accept
      - deny
      - internal-browser-default
    contrast:
      type: enum?
      literals:
      - no-preference
      - more
      - no-override
    baseURL: string?
    recordVideo:
      type: object?
      properties:
        dir: string
        size:
          type: object?
          properties:
            width: number
            height: number
    strictSelectors: boolean?
    serviceWorkers:
      type: enum?
      literals:
      - allow
      - block
    selectorEngines:
      type: array?
      items: SelectorEngine
    testIdAttributeName: string?


LocalUtils:
  type: interface

  initializer:
    deviceDescriptors:
      type: array
      items:
        type: object
        properties:
          name: string
          descriptor:
            type: object
            properties:
              userAgent: string
              viewport:
                type: object
                properties:
                  width: number
                  height: number
              screen:
                type: object?
                properties:
                  width: number
                  height: number
              deviceScaleFactor: number
              isMobile: boolean
              hasTouch: boolean
              defaultBrowserType:
                type: enum
                literals:
                - chromium
                - firefox
                - webkit

  commands:

    zip:
      internal: true
      parameters:
        zipFile: string
        entries:
          type: array
          items: NameValue
        stacksId: string?
        mode:
          type: enum
          literals:
            - write
            - append
        includeSources: boolean

    harOpen:
      internal: true
      parameters:
        file: string
      returns:
        harId: string?
        error: string?

    harLookup:
      internal: true
      parameters:
        harId: string
        url: string
        method: string
        headers:
          type: array
          items: NameValue
        postData: binary?
        isNavigationRequest: boolean
      returns:
        action:
          type: enum
          literals:
          - error
          - redirect
          - fulfill
          - noentry
        message: string?
        redirectURL: string?
        status: number?
        headers:
          type: array?
          items: NameValue
        body: binary?

    harClose:
      internal: true
      parameters:
        harId: string

    harUnzip:
      internal: true
      parameters:
        zipFile: string
        harFile: string

    connect:
      internal: true
      parameters:
        wsEndpoint: string
        headers: json?
        exposeNetwork: string?
        slowMo: number?
        timeout: number
        socksProxyRedirectPortForTest: number?
      returns:
        pipe: JsonPipe
        headers:
          type: array
          items: NameValue

    tracingStarted:
      internal: true
      parameters:
        tracesDir: string?
        traceName: string
      returns:
        stacksId: string

    addStackToTracingNoReply:
      internal: true
      parameters:
        callData: ClientSideCallMetadata

    traceDiscarded:
      internal: true
      parameters:
        stacksId: string

    globToRegex:
      internal: true
      parameters:
        glob: string
        baseURL: string?
        webSocketUrl: boolean?
      returns:
        regex: string

Root:
  type: interface

  commands:

    initialize:
      internal: true
      parameters:
        sdkLanguage:
          type: enum
          literals:
          - javascript
          - python
          - java
          - csharp
      returns:
        playwright: Playwright

Playwright:
  type: interface

  initializer:
    chromium: BrowserType
    firefox: BrowserType
    webkit: BrowserType
    _bidiChromium: BrowserType
    _bidiFirefox: BrowserType
    android: Android
    electron: Electron
    utils: LocalUtils?
    # Only present when connecting remotely via BrowserType.connect() method.
    preLaunchedBrowser: Browser?
    # Only present when connecting remotely via Android.connect() method.
    preConnectedAndroidDevice: AndroidDevice?
    # Only present when socks proxy is supported.
    socksSupport: SocksSupport?

  commands:
    newRequest:
      title: Create request context
      parameters:
        baseURL: string?
        userAgent: string?
        ignoreHTTPSErrors: boolean?
        extraHTTPHeaders:
          type: array?
          items: NameValue
        failOnStatusCode: boolean?
        clientCertificates:
          type: array?
          items:
            type: object
            properties:
              origin: string
              cert: binary?
              key: binary?
              passphrase: string?
              pfx: binary?
        maxRedirects: number?
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?
            send:
              type: enum?
              literals:
              - always
              - unauthorized
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: NetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
        tracesDir: string?

      returns:
        request: APIRequestContext

RecorderSource:
  type: object
  properties:
    isRecorded: boolean
    id: string
    label: string
    text: string
    language: string
    highlight:
      type: array
      items:
        type: object
        properties:
          line: number
          type: string
    revealLine: number?
    group: string?

DebugController:
  type: interface

  commands:
    initialize:
      internal: true
      parameters:
        codegenId: string
        sdkLanguage:
          type: enum
          literals:
          - javascript
          - python
          - java
          - csharp

    setReportStateChanged:
      internal: true
      parameters:
        enabled: boolean

    resetForReuse:
      internal: true

    navigate:
      internal: true
      parameters:
        url: string

    setRecorderMode:
      internal: true
      parameters:
        mode:
          type: enum
          literals:
            - inspecting
            - recording
            - none
        testIdAttributeName: string?

    highlight:
      internal: true
      parameters:
        selector: string?
        ariaTemplate: string?

    hideHighlight:
      internal: true

    resume:
      internal: true

    kill:
      internal: true

    closeAllBrowsers:
      internal: true

  events:
    inspectRequested:
      parameters:
        selector: string
        locator: string
        ariaSnapshot: string

    setModeRequested:
      parameters:
        mode: string

    stateChanged:
      parameters:
        pageCount: number

    sourceChanged:
      parameters:
        text: string
        header: string?
        footer: string?
        actions:
          type: array?
          items: string

    paused:
      parameters:
        paused: boolean

SocksSupport:
  type: interface

  commands:
    socksConnected:
      internal: true
      parameters:
        uid: string
        host: string
        port: number

    socksFailed:
      internal: true
      parameters:
        uid: string
        errorCode: string

    socksData:
      internal: true
      parameters:
        uid: string
        data: binary

    socksError:
      internal: true
      parameters:
        uid: string
        error: string

    socksEnd:
      internal: true
      parameters:
        uid: string

  events:
    socksRequested:
      parameters:
        uid: string
        host: string
        port: number

    socksData:
      parameters:
        uid: string
        data: binary

    socksClosed:
      parameters:
        uid: string


BrowserType:
  type: interface

  initializer:
    executablePath: string
    name: string

  commands:

    launch:
      title: Launch browser
      parameters:
        $mixin: LaunchOptions
        slowMo: number?
      returns:
        browser: Browser

    launchPersistentContext:
      title: Launch persistent context
      parameters:
        $mixin1: LaunchOptions
        $mixin2: ContextOptions
        userDataDir: string
        slowMo: number?
      returns:
        browser: Browser
        context: BrowserContext

    connectOverCDP:
      title: Connect over CDP
      parameters:
        endpointURL: string
        headers:
          type: array?
          items: NameValue
        slowMo: number?
        timeout: number
      returns:
        browser: Browser
        defaultContext: BrowserContext?

Browser:
  type: interface

  initializer:
    version: string
    name: string

  commands:

    close:
      title: Close browser
      parameters:
        reason: string?

    killForTests:
      internal: true

    defaultUserAgentForTest:
      internal: true
      returns:
        userAgent: string

    newContext:
      title: Create context
      parameters:
        $mixin: ContextOptions
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: SetNetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
      returns:
        context: BrowserContext

    newContextForReuse:
      internal: true
      parameters:
        $mixin: ContextOptions
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?
        storageState:
          type: object?
          properties:
            cookies:
              type: array?
              items: SetNetworkCookie
            origins:
              type: array?
              items: SetOriginStorage
      returns:
        context: BrowserContext

    disconnectFromReusedContext:
      internal: true
      parameters:
        reason: string

    newBrowserCDPSession:
      title: Create CDP session
      internal: true
      returns:
        session: CDPSession

    startTracing:
      internal: true
      parameters:
        page: Page?
        screenshots: boolean?
        categories:
          type: array?
          items: string

    stopTracing:
      internal: true
      returns:
        artifact: Artifact

  events:

    context:
      parameters:
        context: BrowserContext

    close:

ConsoleMessage:
  type: mixin
  properties:
    type: string
    text: string
    args:
      type: array
      items: JSHandle
    location:
      type: object
      properties:
        url: string
        lineNumber: number
        columnNumber: number


EventTarget:
  type: interface

  commands:
    waitForEventInfo:
      title: Wait for event "{info.event}"
      parameters:
        info:
          type: object
          properties:
            waitId: string
            phase:
              type: enum
              literals:
              - before
              - after
              - log
            event: string?
            message: string?
            error: string?
      flags:
        snapshot: true

BrowserContext:
  type: interface

  extends: EventTarget

  initializer:
    isChromium: boolean
    requestContext: APIRequestContext
    tracing: Tracing
    options:
      type: object
      properties:
        $mixin: ContextOptions

  commands:

    addCookies:
      title: Add cookies
      parameters:
        cookies:
          type: array
          items: SetNetworkCookie

    addInitScript:
      title: Add init script
      parameters:
        source: string

    clearCookies:
      title: Clear cookies
      parameters:
        name: string?
        nameRegexSource: string?
        nameRegexFlags: string?
        domain: string?
        domainRegexSource: string?
        domainRegexFlags: string?
        path: string?
        pathRegexSource: string?
        pathRegexFlags: string?

    clearPermissions:
      title: Clear permissions

    close:
      title: Close context
      parameters:
        reason: string?

    cookies:
      title: Get cookies
      parameters:
        urls:
          type: array
          items: string
      returns:
        cookies:
          type: array
          items: NetworkCookie

    exposeBinding:
      title: Expose binding
      parameters:
        name: string
        needsHandle: boolean?

    grantPermissions:
      title: Grant permissions
      parameters:
        permissions:
          type: array
          items: string
        origin: string?

    newPage:
      title: Create page
      returns:
        page: Page

    registerSelectorEngine:
      internal: true
      parameters:
        selectorEngine: SelectorEngine

    setTestIdAttributeName:
      internal: true
      parameters:
        testIdAttributeName: string

    setExtraHTTPHeaders:
      title: Set extra HTTP headers
      parameters:
        headers:
          type: array
          items: NameValue

    setGeolocation:
      title: Set geolocation
      parameters:
        geolocation:
          type: object?
          properties:
            longitude: number
            latitude: number
            accuracy: number?

    setHTTPCredentials:
      title: Set HTTP credentials
      parameters:
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?

    setNetworkInterceptionPatterns:
      internal: true
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setWebSocketInterceptionPatterns:
      internal: true
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setOffline:
      title: Set offline mode
      parameters:
        offline: boolean

    storageState:
      title: Get storage state
      parameters:
        indexedDB: boolean?
      returns:
        cookies:
          type: array
          items: NetworkCookie
        origins:
          type: array
          items: OriginStorage

    pause:
      title: Pause

    enableRecorder:
      internal: true
      parameters:
        language: string?
        mode:
          type: enum?
          literals:
          - inspecting
          - recording
        recorderMode:
          type: enum?
          literals:
          - default
          - api
        pauseOnNextStatement: boolean?
        testIdAttributeName: string?
        launchOptions: json?
        contextOptions: json?
        device: string?
        saveStorage: string?
        outputFile: string?
        handleSIGINT: boolean?
        omitCallTracking: boolean?

    disableRecorder:
      internal: true

    newCDPSession:
      internal: true
      parameters:
        page: Page?
        frame: Frame?
      returns:
        session: CDPSession

    harStart:
      internal: true
      parameters:
        page: Page?
        options: RecordHarOptions
      returns:
        harId: string

    harExport:
      internal: true
      parameters:
        harId: string?
      returns:
        artifact: Artifact

    createTempFiles:
      internal: true
      parameters:
        rootDirName: string?
        items:
          type: array
          items:
            type: object
            properties:
              name: string
              lastModifiedMs: number?
      returns:
        rootDir: WritableStream?
        writableStreams:
          type: array
          items: WritableStream

    updateSubscription:
      internal: true
      parameters:
        event:
          type: enum
          literals:
          - console
          - dialog
          - request
          - response
          - requestFinished
          - requestFailed
        enabled: boolean

    clockFastForward:
      title: Fast forward clock "{ticksNumber}{ticksString}"
      parameters:
        ticksNumber: number?
        ticksString: string?

    clockInstall:
      title: Install clock "{timeNumber}{timeString}"
      parameters:
        timeNumber: number?
        timeString: string?

    clockPauseAt:
      title: Pause clock "{timeNumber}{timeString}"
      parameters:
        timeNumber: number?
        timeString: string?

    clockResume:
      title: Resume clock

    clockRunFor:
      title: Run clock "{ticksNumber}{ticksString}"
      parameters:
        ticksNumber: number?
        ticksString: string?

    clockSetFixedTime:
      title: Set fixed time "{timeNumber}{timeString}"
      parameters:
        timeNumber: number?
        timeString: string?

    clockSetSystemTime:
      title: Set system time "{timeNumber}{timeString}"
      parameters:
        timeNumber: number?
        timeString: string?

  events:

    bindingCall:
      parameters:
        binding: BindingCall

    console:
      parameters:
        $mixin: ConsoleMessage
        page: Page

    close:

    dialog:
      parameters:
        dialog: Dialog

    page:
      parameters:
        page: Page

    pageError:
      parameters:
        error: SerializedError
        page: Page

    route:
      parameters:
        route: Route

    webSocketRoute:
      parameters:
        webSocketRoute: WebSocketRoute

    video:
      parameters:
        artifact: Artifact

    backgroundPage:
      parameters:
        page: Page

    serviceWorker:
      parameters:
        worker: Worker

    request:
      parameters:
        request: Request
        page: Page?

    requestFailed:
      parameters:
        request: Request
        failureText: string?
        responseEndTiming: number
        page: Page?

    requestFinished:
      parameters:
        request: Request
        response: Response?
        responseEndTiming: number
        page: Page?

    response:
      parameters:
        response: Response
        page: Page?

    recorderEvent:
      parameters:
        event:
          type: enum
          literals:
          - actionAdded
          - actionUpdated
          - signalAdded
        data: json
        page: Page

Page:
  type: interface

  extends: EventTarget

  initializer:
    mainFrame: Frame
    viewportSize:
      type: object?
      properties:
        width: number
        height: number
    isClosed: boolean
    opener: Page?

  commands:

    addInitScript:
      parameters:
        source: string

    close:
      title: Close
      parameters:
        runBeforeUnload: boolean?
        reason: string?

    emulateMedia:
      title: Emulate media
      parameters:
        media:
          type: enum?
          literals:
          - screen
          - print
          - no-override
        colorScheme:
          type: enum?
          literals:
          - dark
          - light
          - no-preference
          - no-override
        reducedMotion:
          type: enum?
          literals:
          - reduce
          - no-preference
          - no-override
        forcedColors:
          type: enum?
          literals:
          - active
          - none
          - no-override
        contrast:
          type: enum?
          literals:
          - no-preference
          - more
          - no-override
      flags:
        snapshot: true

    exposeBinding:
      title: Expose binding
      parameters:
        name: string
        needsHandle: boolean?

    goBack:
      title: Go back
      parameters:
        timeout: number
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    goForward:
      title: Go forward
      parameters:
        timeout: number
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    requestGC:
      title: Request garbage collection

    registerLocatorHandler:
      title: Register locator handler
      parameters:
        selector: string
        noWaitAfter: boolean?
      returns:
        uid: number

    resolveLocatorHandlerNoReply:
      internal: true
      parameters:
        uid: number
        remove: boolean?

    unregisterLocatorHandler:
      title: Unregister locator handler
      parameters:
        uid: number

    reload:
      title: Reload
      parameters:
        timeout: number
        waitUntil: LifecycleEvent?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    expectScreenshot:
      title: Expect screenshot
      parameters:
        expected: binary?
        timeout: number
        isNot: boolean
        locator:
          type: object?
          properties:
            frame: Frame
            selector: string
        comparator: string?
        maxDiffPixels: number?
        maxDiffPixelRatio: number?
        threshold: number?
        fullPage: boolean?
        clip: Rect?
        $mixin: CommonScreenshotOptions
      returns:
        diff: binary?
        errorMessage: string?
        actual: binary?
        previous: binary?
        timedOut: boolean?
        log:
          type: array?
          items: string
      flags:
        snapshot: true

    screenshot:
      title: Screenshot
      parameters:
        timeout: number
        type:
          type: enum?
          literals:
          - png
          - jpeg
        quality: number?
        fullPage: boolean?
        clip: Rect?
        $mixin: CommonScreenshotOptions
      returns:
        binary: binary
      flags:
        snapshot: true

    setExtraHTTPHeaders:
      title: Set extra HTTP headers
      parameters:
        headers:
          type: array
          items: NameValue

    setNetworkInterceptionPatterns:
      internal: true
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setWebSocketInterceptionPatterns:
      internal: true
      parameters:
        patterns:
          type: array
          items:
            type: object
            properties:
              glob: string?
              regexSource: string?
              regexFlags: string?

    setViewportSize:
      title: Set viewport size
      parameters:
        viewportSize:
          type: object
          properties:
            width: number
            height: number
      flags:
        snapshot: true

    keyboardDown:
      title: Key down "{key}"
      parameters:
        key: string
      flags:
        slowMo: true
        snapshot: true

    keyboardUp:
      title: Key up "{key}"
      parameters:
        key: string
      flags:
        slowMo: true
        snapshot: true

    keyboardInsertText:
      title: Insert "{text}"
      parameters:
        text: string
      flags:
        slowMo: true
        snapshot: true

    keyboardType:
      title: Type "{text}"
      parameters:
        text: string
        delay: number?
      flags:
        slowMo: true
        snapshot: true

    keyboardPress:
      title: Press "{key}"
      parameters:
        key: string
        delay: number?
      flags:
        slowMo: true
        snapshot: true

    mouseMove:
      title: Mouse move
      parameters:
        x: number
        y: number
        steps: number?
      flags:
        slowMo: true
        snapshot: true

    mouseDown:
      title: Mouse down
      parameters:
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseUp:
      title: Mouse up
      parameters:
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseClick:
      title: Click
      parameters:
        x: number
        y: number
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
      flags:
        slowMo: true
        snapshot: true

    mouseWheel:
      title: Mouse wheel
      parameters:
        deltaX: number
        deltaY: number
      flags:
        slowMo: true
        snapshot: true

    touchscreenTap:
      title: Tap
      parameters:
        x: number
        y: number
      flags:
        slowMo: true
        snapshot: true

    accessibilitySnapshot:
      internal: true
      parameters:
        interestingOnly: boolean?
        root: ElementHandle?
      returns:
        rootAXNode: AXNode?
      flags:
        snapshot: true

    pdf:
      title: PDF
      parameters:
        scale: number?
        displayHeaderFooter: boolean?
        headerTemplate: string?
        footerTemplate: string?
        printBackground: boolean?
        landscape: boolean?
        pageRanges: string?
        format: string?
        width: string?
        height: string?
        preferCSSPageSize: boolean?
        margin:
          type: object?
          properties:
            top: string?
            bottom: string?
            left: string?
            right: string?
        tagged: boolean?
        outline: boolean?
      returns:
        pdf: binary

    snapshotForAI:
      internal: true
      returns:
        snapshot: string
      flags:
        snapshot: true

    startJSCoverage:
      internal: true
      parameters:
        resetOnNavigation: boolean?
        reportAnonymousScripts: boolean?

    stopJSCoverage:
      internal: true
      returns:
        entries:
          type: array
          items:
            type: object
            properties:
              url: string
              scriptId: string
              source: string?
              functions:
                type: array
                items:
                  type: object
                  properties:
                    functionName: string
                    isBlockCoverage: boolean
                    ranges:
                      type: array
                      items:
                        type: object
                        properties:
                          startOffset: number
                          endOffset: number
                          count: number

    startCSSCoverage:
      internal: true
      parameters:
        resetOnNavigation: boolean?

    stopCSSCoverage:
      internal: true
      returns:
        entries:
          type: array
          items:
            type: object
            properties:
              url: string
              text: string?
              ranges:
                type: array
                items:
                  type: object
                  properties:
                    start: number
                    end: number

    bringToFront:
      title: Bring to front

    updateSubscription:
      internal: true
      parameters:
        event:
          type: enum
          literals:
          - console
          - dialog
          - fileChooser
          - request
          - response
          - requestFinished
          - requestFailed
        enabled: boolean

  events:

    bindingCall:
      parameters:
        binding: BindingCall

    close:

    crash:

    download:
      parameters:
        url: string
        suggestedFilename: string
        artifact: Artifact

    viewportSizeChanged:
      parameters:
        viewportSize:
          type: object?
          properties:
            width: number
            height: number

    fileChooser:
      parameters:
        element: ElementHandle
        isMultiple: boolean

    frameAttached:
      parameters:
        frame: Frame

    frameDetached:
      parameters:
        frame: Frame

    locatorHandlerTriggered:
      parameters:
        uid: number

    route:
      parameters:
        route: Route

    webSocketRoute:
      parameters:
        webSocketRoute: WebSocketRoute

    video:
      parameters:
        artifact: Artifact

    webSocket:
      parameters:
        webSocket: WebSocket

    worker:
      parameters:
        worker: Worker



Frame:
  type: interface

  initializer:
    url: string
    name: string
    parentFrame: Frame?
    loadStates:
      type: array
      items: LifecycleEvent

  commands:

    evalOnSelector:
      title: Evaluate
      parameters:
        selector: string
        strict: boolean?
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evalOnSelectorAll:
      title: Evaluate
      parameters:
        selector: string
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    addScriptTag:
      title: Add script tag
      parameters:
        url: string?
        content: string?
        type: string?
      returns:
        element: ElementHandle
      flags:
        snapshot: true

    addStyleTag:
      title: Add style tag
      parameters:
        url: string?
        content: string?
      returns:
        element: ElementHandle
      flags:
        snapshot: true

    ariaSnapshot:
      title: Aria snapshot
      parameters:
        selector: string
        forAI: boolean?
        timeout: number
      returns:
        snapshot: string
      flags:
        snapshot: true

    blur:
      title: Blur
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true

    check:
      title: Check
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    click:
      title: Click
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        noWaitAfter: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    content:
      title: Get content
      returns:
        value: string
      flags:
        snapshot: true

    dragAndDrop:
      title: Drag and drop
      parameters:
        source: string
        target: string
        force: boolean?
        timeout: number
        trial: boolean?
        sourcePosition: Point?
        targetPosition: Point?
        strict: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dblclick:
      title: Double click
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dispatchEvent:
      title: Dispatch "{type}"
      parameters:
        selector: string
        strict: boolean?
        type: string
        eventInit: SerializedArgument
        timeout: number
      flags:
        slowMo: true
        snapshot: true

    evaluateExpression:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evaluateExpressionHandle:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    fill:
      title: Fill "{value}"
      parameters:
        selector: string
        strict: boolean?
        value: string
        force: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    focus:
      title: Focus
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true

    frameElement:
      internal: true
      returns:
        element: ElementHandle

    generateLocatorString:
      internal: true
      parameters:
        selector: string
      returns:
        value: string?

    highlight:
      internal: true
      parameters:
        selector: string

    getAttribute:
      internal: true
      parameters:
        selector: string
        strict: boolean?
        name: string
        timeout: number
      returns:
        value: string?
      flags:
        snapshot: true

    goto:
      title: Navigate to "{url}"
      parameters:
        url: string
        timeout: number
        waitUntil: LifecycleEvent?
        referer: string?
      returns:
        response: Response?
      flags:
        slowMo: true
        snapshot: true

    hover:
      title: Hover
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    innerHTML:
      title: Get HTML
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: string
      flags:
        snapshot: true

    innerText:
      title: Get inner text
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: string
      flags:
        snapshot: true

    inputValue:
      title: Get input value
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: string
      flags:
        snapshot: true

    isChecked:
      title: Is checked
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: boolean
      flags:
        snapshot: true

    isDisabled:
      title: Is disabled
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: boolean
      flags:
        snapshot: true

    isEnabled:
      title: Is enabled
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: boolean
      flags:
        snapshot: true

    isHidden:
      title: Is hidden
      parameters:
        # Note: compared to other isFoo methods, isHidden is a one-shot operation without a timeout.
        selector: string
        strict: boolean?
      returns:
        value: boolean
      flags:
        snapshot: true

    isVisible:
      title: Is visible
      parameters:
        # Note: compared to other isFoo methods, isVisible is a one-shot operation without a timeout.
        selector: string
        strict: boolean?
      returns:
        value: boolean
      flags:
        snapshot: true

    isEditable:
      title: Is editable
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: boolean
      flags:
        snapshot: true

    press:
      title: Press "{key}"
      parameters:
        selector: string
        strict: boolean?
        key: string
        delay: number?
        noWaitAfter: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    querySelector:
      title: Query selector
      parameters:
        selector: string
        strict: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    querySelectorAll:
      title: Query selector all
      parameters:
        selector: string
      returns:
        elements:
          type: array
          items: ElementHandle
      flags:
        snapshot: true

    queryCount:
      title: Query count
      parameters:
        selector: string
      returns:
        value: number
      flags:
        snapshot: true

    selectOption:
      title: Select option
      parameters:
        selector: string
        strict: boolean?
        elements:
          type: array?
          items: ElementHandle
        options:
          type: array?
          items:
            type: object
            properties:
              valueOrLabel: string?
              value: string?
              label: string?
              index: number?
        force: boolean?
        timeout: number
      returns:
        values:
          type: array
          items: string
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    setContent:
      title: Set content
      parameters:
        html: string
        timeout: number
        waitUntil: LifecycleEvent?
      flags:
        snapshot: true

    setInputFiles:
      title: Set input files
      parameters:
        selector: string
        strict: boolean?
        # Only one of payloads, localPaths and streams should be present.
        payloads:
          type: array?
          items:
            type: object
            properties:
              name: string
              mimeType: string?
              buffer: binary
        localDirectory: string?
        directoryStream: WritableStream?
        localPaths:
          type: array?
          items: string
        streams:
          type: array?
          items: WritableStream
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    tap:
      title: Tap
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    textContent:
      title: Get text content
      parameters:
        selector: string
        strict: boolean?
        timeout: number
      returns:
        value: string?
      flags:
        snapshot: true

    title:
      internal: true
      returns:
        value: string

    type:
      title: Type
      parameters:
        selector: string
        strict: boolean?
        text: string
        delay: number?
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    uncheck:
      title: Uncheck
      parameters:
        selector: string
        strict: boolean?
        force: boolean?
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    waitForTimeout:
      title: Wait for timeout
      parameters:
        waitTimeout: number
      flags:
        snapshot: true

    waitForFunction:
      title: Wait for function
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
        timeout: number
        # When present, polls on interval. Otherwise, polls on raf.
        pollingInterval: number?
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    waitForSelector:
      title: Wait for selector
      parameters:
        selector: string
        strict: boolean?
        timeout: number
        state:
          type: enum?
          literals:
          - attached
          - detached
          - visible
          - hidden
        omitReturnValue: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    expect:
      title: Expect "{expression}"
      parameters:
        selector: string?
        expression: string
        expressionArg: json?
        expectedText:
          type: array?
          items: ExpectedTextValue
        expectedNumber: number?
        expectedValue: SerializedArgument?
        useInnerText: boolean?
        isNot: boolean
        timeout: number
      returns:
        matches: boolean
        received: SerializedValue?
        timedOut: boolean?
        log:
          type: array?
          items: string
      flags:
        snapshot: true

  events:

    loadstate:
      parameters:
        add: LifecycleEvent?
        remove: LifecycleEvent?

    navigated:
      parameters:
        url: string
        name: string
        newDocument:
          type: object?
          properties:
            request: Request?
        error: string?



Worker:
  type: interface

  initializer:
    url: string

  commands:

    evaluateExpression:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue

    evaluateExpressionHandle:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle

  events:

    close:


JSHandle:
  type: interface

  initializer:
    preview: string

  commands:

    dispose:

    evaluateExpression:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evaluateExpressionHandle:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle
      flags:
        snapshot: true

    getPropertyList:
      internal: true
      returns:
        properties:
          type: array
          items:
            type: object
            properties:
              name: string
              value: JSHandle

    getProperty:
      internal: true
      parameters:
        name: string
      returns:
        handle: JSHandle

    jsonValue:
      internal: true
      returns:
        value: SerializedValue

  events:

    previewUpdated:
      parameters:
        preview: string



ElementHandle:
  type: interface

  extends: JSHandle

  commands:

    evalOnSelector:
      title: Evaluate
      parameters:
        selector: string
        strict: boolean?
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    evalOnSelectorAll:
      title: Evaluate
      parameters:
        selector: string
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue
      flags:
        snapshot: true

    boundingBox:
      title: Get bounding box
      returns:
        value: Rect?
      flags:
        snapshot: true

    check:
      title: Check
      parameters:
        force: boolean?
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    click:
      title: Click
      parameters:
        force: boolean?
        noWaitAfter: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        clickCount: number?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    contentFrame:
      internal: true
      returns:
        frame: Frame?
      flags:
        snapshot: true

    dblclick:
      title: Double click
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        delay: number?
        button:
          type: enum?
          literals:
          - left
          - right
          - middle
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    dispatchEvent:
      title: Dispatch event
      parameters:
        type: string
        eventInit: SerializedArgument
      flags:
        slowMo: true
        snapshot: true

    fill:
      title: Fill "{value}"
      parameters:
        value: string
        force: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    focus:
      title: Focus
      flags:
        slowMo: true
        snapshot: true

    getAttribute:
      internal: true
      parameters:
        name: string
      returns:
        value: string?

    hover:
      title: Hover
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    innerHTML:
      title: Get HTML
      returns:
        value: string
      flags:
        snapshot: true

    innerText:
      title: Get inner text
      returns:
        value: string
      flags:
        snapshot: true

    inputValue:
      title: Get input value
      returns:
        value: string
      flags:
        snapshot: true

    isChecked:
      title: Is checked
      returns:
        value: boolean
      flags:
        snapshot: true

    isDisabled:
      title: Is disabled
      returns:
        value: boolean
      flags:
        snapshot: true

    isEditable:
      title: Is editable
      returns:
        value: boolean
      flags:
        snapshot: true

    isEnabled:
      title: Is enabled
      returns:
        value: boolean
      flags:
        snapshot: true

    isHidden:
      title: Is hidden
      returns:
        value: boolean
      flags:
        snapshot: true

    isVisible:
      title: Is visible
      returns:
        value: boolean
      flags:
        snapshot: true

    ownerFrame:
      title: Get owner frame
      returns:
        frame: Frame?

    press:
      title: Press "{key}"
      parameters:
        key: string
        delay: number?
        timeout: number
        noWaitAfter: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    querySelector:
      title: Query selector
      parameters:
        selector: string
        strict: boolean?
      returns:
        element: ElementHandle?
      flags:
        snapshot: true

    querySelectorAll:
      title: Query selector all
      parameters:
        selector: string
      returns:
        elements:
          type: array
          items: ElementHandle
      flags:
        snapshot: true

    screenshot:
      title: Screenshot
      parameters:
        timeout: number
        type:
          type: enum?
          literals:
          - png
          - jpeg
        quality: number?
        $mixin: CommonScreenshotOptions
      returns:
        binary: binary
      flags:
        snapshot: true

    scrollIntoViewIfNeeded:
      title: Scroll into view
      parameters:
        timeout: number
      flags:
        slowMo: true
        snapshot: true

    selectOption:
      title: Select option
      parameters:
        elements:
          type: array?
          items: ElementHandle
        options:
          type: array?
          items:
            type: object
            properties:
              valueOrLabel: string?
              value: string?
              label: string?
              index: number?
        force: boolean?
        timeout: number
      returns:
        values:
          type: array
          items: string
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    selectText:
      title: Select text
      parameters:
        force: boolean?
        timeout: number
      flags:
        slowMo: true
        snapshot: true

    setInputFiles:
      title: Set input files
      parameters:
        # Only one of payloads, localPaths and streams should be present.
        payloads:
          type: array?
          items:
            type: object
            properties:
              name: string
              mimeType: string?
              buffer: binary
        localDirectory: string?
        directoryStream: WritableStream?
        localPaths:
          type: array?
          items: string
        streams:
          type: array?
          items: WritableStream
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    tap:
      title: Tap
      parameters:
        force: boolean?
        modifiers:
          type: array?
          items:
            type: enum
            literals:
            - Alt
            - Control
            - ControlOrMeta
            - Meta
            - Shift
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    textContent:
      title: Get text content
      returns:
        value: string?
      flags:
        snapshot: true

    type:
      title: Type
      parameters:
        text: string
        delay: number?
        timeout: number
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    uncheck:
      title: Uncheck
      parameters:
        force: boolean?
        position: Point?
        timeout: number
        trial: boolean?
      flags:
        slowMo: true
        snapshot: true
        pausesBeforeInput: true

    waitForElementState:
      title: Wait for state
      parameters:
        state:
          type: enum
          literals:
          - visible
          - hidden
          - stable
          - enabled
          - disabled
          - editable
        timeout: number
      flags:
        snapshot: true

    waitForSelector:
      title: Wait for selector
      parameters:
        selector: string
        strict: boolean?
        timeout: number
        state:
          type: enum?
          literals:
          - attached
          - detached
          - visible
          - hidden
      returns:
        element: ElementHandle?
      flags:
        snapshot: true


Request:
  type: interface

  initializer:
    frame: Frame?
    serviceWorker: Worker?
    url: string
    resourceType: string
    method: string
    postData: binary?
    headers:
      type: array
      items: NameValue
    isNavigationRequest: boolean
    redirectedFrom: Request?

  commands:

    response:
      internal: true
      returns:
        response: Response?

    rawRequestHeaders:
      internal: true
      returns:
        headers:
          type: array
          items: NameValue


Route:
  type: interface

  initializer:
    request: Request

  commands:

    redirectNavigationRequest:
      internal: true
      parameters:
        url: string

    abort:
      parameters:
        errorCode: string?

    continue:
      internal: true
      parameters:
        url: string?
        method: string?
        headers:
          type: array?
          items: NameValue
        postData: binary?
        isFallback: boolean

    fulfill:
      internal: true
      parameters:
        # default is 200
        status: number?
        headers:
          type: array?
          items: NameValue
        body: string?
        isBase64: boolean?
        fetchResponseUid: string?


WebSocketRoute:
  type: interface

  initializer:
    url: string

  commands:

    connect:
      internal: true

    ensureOpened:
      internal: true

    sendToPage:
      internal: true
      parameters:
        message: string
        isBase64: boolean

    sendToServer:
      internal: true
      parameters:
        message: string
        isBase64: boolean

    closePage:
      internal: true
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

    closeServer:
      internal: true
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

  events:

    messageFromPage:
      parameters:
        message: string
        isBase64: boolean

    messageFromServer:
      parameters:
        message: string
        isBase64: boolean

    closePage:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean

    closeServer:
      parameters:
        code: number?
        reason: string?
        wasClean: boolean


ResourceTiming:
  type: object
  properties:
    startTime: number
    domainLookupStart: number
    domainLookupEnd: number
    connectStart: number
    secureConnectionStart: number
    connectEnd: number
    requestStart: number
    responseStart: number

Response:
  type: interface

  initializer:
    request: Request
    url: string
    status: number
    statusText: string
    headers:
      type: array
      items: NameValue
    timing: ResourceTiming
    fromServiceWorker: boolean


  commands:

    body:
      internal: true
      returns:
        binary: binary

    securityDetails:
      internal: true
      returns:
        value: SecurityDetails?

    serverAddr:
      internal: true
      returns:
        value: RemoteAddr?

    rawResponseHeaders:
      internal: true
      returns:
        headers:
          type: array
          items: NameValue

    sizes:
      internal: true
      returns:
        sizes: RequestSizes


SecurityDetails:
  type: object
  properties:
    issuer: string?
    protocol: string?
    subjectName: string?
    validFrom: number?
    validTo: number?

RequestSizes:
  type: object
  properties:
    requestBodySize: number
    requestHeadersSize: number
    responseBodySize: number
    responseHeadersSize: number


RemoteAddr:
  type: object
  properties:
    ipAddress: string
    port: number


WebSocket:
  type: interface

  extends: EventTarget

  initializer:
    url: string

  events:
    open:

    frameSent:
      parameters:
        opcode: number
        data: string

    frameReceived:
      parameters:
        opcode: number
        data: string

    socketError:
      parameters:
        error: string

    close:


BindingCall:
  type: interface

  initializer:
    frame: Frame
    name: string
    args:
      type: array?
      items: SerializedValue
    handle: JSHandle?

  commands:

    reject:
      internal: true
      parameters:
        error: SerializedError

    resolve:
      internal: true
      parameters:
        result: SerializedArgument



Dialog:
  type: interface

  initializer:
    page: Page?
    type: string
    message: string
    defaultValue: string

  commands:

    accept:
      title: Accept dialog
      parameters:
        promptText: string?

    dismiss:
      title: Dismiss dialog

Tracing:
  type: interface

  commands:

    tracingStart:
      internal: true
      parameters:
        name: string?
        snapshots: boolean?
        screenshots: boolean?
        live: boolean?

    tracingStartChunk:
      internal: true
      parameters:
        name: string?
        title: string?
      returns:
        traceName: string

    tracingGroup:
      title: Trace "{name}"
      parameters:
        name: string
        location:
          type: object?
          properties:
            file: string
            line: number?
            column: number?

    tracingGroupEnd:
      title: Group end

    tracingStopChunk:
      internal: true
      parameters:
        mode:
          type: enum
          literals:
          - archive
          - discard
          - entries
      returns:
        # The artifact may be missing if the browser closes while tracing is being stopped.
        # Or it can be missing if client-side compression is taking place.
        artifact: Artifact?
        # For local mode, these are all entries.
        entries:
          type: array?
          items: NameValue

    tracingStop:
      internal: true


Artifact:
  type: interface

  initializer:
    absolutePath: string

  commands:

    pathAfterFinished:
      internal: true
      returns:
        value: string

    # Blocks path/failure/delete/context.close until saved to the local |path|.
    saveAs:
      internal: true
      parameters:
        path: string

    # Blocks path/failure/delete/context.close until the stream is closed.
    saveAsStream:
      internal: true
      returns:
        stream: Stream

    failure:
      internal: true
      returns:
        error: string?

    stream:
      internal: true
      returns:
        stream: Stream

    cancel:
      internal: true

    delete:
      internal: true


Stream:
  type: interface

  commands:

    read:
      internal: true
      parameters:
        size: number?
      returns:
        binary: binary

    close:
      internal: true


WritableStream:
  type: interface

  commands:

    write:
      internal: true
      parameters:
        binary: binary

    close:
      internal: true


CDPSession:
  type: interface

  commands:

    send:
      internal: true
      parameters:
        method: string
        params: json?
      returns:
        result: json

    detach:
      internal: true

  events:

    event:
      parameters:
        method: string
        params: json?


Electron:
  type: interface

  commands:

    launch:
      title: Launch electron
      parameters:
        executablePath: string?
        args:
          type: array?
          items: string
        cwd: string?
        env:
          type: array?
          items: NameValue
        timeout: number
        acceptDownloads:
          type: enum?
          literals:
          - accept
          - deny
          - internal-browser-default
        bypassCSP: boolean?
        colorScheme:
          type: enum?
          literals:
          - dark
          - light
          - no-preference
          - no-override
        extraHTTPHeaders:
          type: array?
          items: NameValue
        geolocation:
          type: object?
          properties:
            longitude: number
            latitude: number
            accuracy: number?
        httpCredentials:
          type: object?
          properties:
            username: string
            password: string
            origin: string?
        ignoreHTTPSErrors: boolean?
        locale: string?
        offline: boolean?
        recordVideo:
          type: object?
          properties:
            dir: string
            size:
              type: object?
              properties:
                width: number
                height: number
        strictSelectors: boolean?
        timezoneId: string?
        tracesDir: string?
        selectorEngines:
          type: array?
          items: SelectorEngine
        testIdAttributeName: string?

      returns:
        electronApplication: ElectronApplication


ElectronApplication:
  type: interface

  extends: EventTarget

  initializer:
    context: BrowserContext

  commands:

    browserWindow:
      internal: true
      parameters:
        page: Page
      returns:
        handle: JSHandle

    evaluateExpression:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        value: SerializedValue

    evaluateExpressionHandle:
      title: Evaluate
      parameters:
        expression: string
        isFunction: boolean?
        arg: SerializedArgument
      returns:
        handle: JSHandle

    updateSubscription:
      internal: true
      parameters:
        event:
          type: enum
          literals:
          - console
        enabled: boolean

  events:
    close:
    console:
      parameters:
        $mixin: ConsoleMessage

Android:
  type: interface

  commands:

    devices:
      internal: true
      parameters:
        host: string?
        port: number?
        omitDriverInstall: boolean?
      returns:
        devices:
          type: array
          items: AndroidDevice

AndroidSocket:
  type: interface

  commands:
    write:
      internal: true
      parameters:
        data: binary

    close:
      internal: true

  events:
    data:
      parameters:
        data: binary
    close:

AndroidDevice:
  type: interface

  extends: EventTarget

  initializer:
    model: string
    serial: string

  commands:
    wait:
      hidden: Wait
      parameters:
        androidSelector: AndroidSelector
        state:
          type: enum?
          literals:
          - gone
        timeout: number

    fill:
      title: Fill "{text}"
      parameters:
        androidSelector: AndroidSelector
        text: string
        timeout: number

    tap:
      title: Tap
      parameters:
        androidSelector: AndroidSelector
        duration: number?
        timeout: number

    drag:
      title: Drag
      parameters:
        androidSelector: AndroidSelector
        dest: Point
        speed: number?
        timeout: number

    fling:
      title: Fling
      parameters:
        androidSelector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        speed: number?
        timeout: number

    longTap:
      title: Long tap
      parameters:
        androidSelector: AndroidSelector
        timeout: number

    pinchClose:
      title: Pinch close
      parameters:
        androidSelector: AndroidSelector
        percent: number
        speed: number?
        timeout: number

    pinchOpen:
      title: Pinch open
      parameters:
        androidSelector: AndroidSelector
        percent: number
        speed: number?
        timeout: number

    scroll:
      title: Scroll
      parameters:
        androidSelector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        percent: number
        speed: number?
        timeout: number

    swipe:
      title: Swipe
      parameters:
        androidSelector: AndroidSelector
        direction:
          type: enum
          literals:
          - up
          - down
          - left
          - right
        percent: number
        speed: number?
        timeout: number

    info:
      internal: true
      parameters:
        androidSelector: AndroidSelector
      returns:
        info: AndroidElementInfo

    screenshot:
      title: Screenshot
      returns:
        binary: binary

    inputType:
      title: Type
      parameters:
        text: string

    inputPress:
      title: Press
      parameters:
        key: string

    inputTap:
      title: Tap
      parameters:
        point: Point

    inputSwipe:
      title: Swipe
      parameters:
        segments:
          type: array
          items: Point
        steps: number

    inputDrag:
      title: Drag
      parameters:
        from: Point
        to: Point
        steps: number

    launchBrowser:
      title: Launch browser
      parameters:
        $mixin: ContextOptions
        pkg: string?
        args:
          type: array?
          items: string
        proxy:
          type: object?
          properties:
            server: string
            bypass: string?
            username: string?
            password: string?

      returns:
        context: BrowserContext

    open:
      title: Open app
      parameters:
        command: string
      returns:
        socket: AndroidSocket

    shell:
      internal: true
      parameters:
        command: string
      returns:
        result: binary

    installApk:
      title: Install apk
      parameters:
        file: binary
        args:
          type: array?
          items: string

    push:
      title: Push
      parameters:
        file: binary
        path: string
        mode: number?

    connectToWebView:
      internal: true
      parameters:
        socketName: string
      returns:
        context: BrowserContext

    close:
      internal: true

  events:
    close:

    webViewAdded:
      parameters:
        webView: AndroidWebView

    webViewRemoved:
      parameters:
        socketName: string


AndroidWebView:
  type: object
  properties:
    pid: number
    pkg: string
    socketName: string


AndroidSelector:
  type: object
  properties:
    checkable: boolean?
    checked: boolean?
    clazz: string?
    clickable: boolean?
    depth: number?
    desc: string?
    enabled: boolean?
    focusable: boolean?
    focused: boolean?
    hasChild:
      type: object?
      properties:
        androidSelector: AndroidSelector
    hasDescendant:
      type: object?
      properties:
        androidSelector: AndroidSelector
        maxDepth: number?
    longClickable: boolean?
    pkg: string?
    res: string?
    scrollable: boolean?
    selected: boolean?
    text: string?


AndroidElementInfo:
  type: object
  properties:
    children:
      type: array?
      items: AndroidElementInfo
    clazz: string
    desc: string
    res: string
    pkg: string
    text: string
    bounds: Rect
    checkable: boolean
    checked: boolean
    clickable: boolean
    enabled: boolean
    focusable: boolean
    focused: boolean
    longClickable: boolean
    scrollable: boolean
    selected: boolean


JsonPipe:
  type: interface

  commands:
    send:
      internal: true
      parameters:
        message: json

    close:
      internal: true

  events:

    message:
      parameters:
        message: json

    closed:
      parameters:
        reason: string?
